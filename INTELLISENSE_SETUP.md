# C++ IntelliSense 配置指南

## 问题描述
VSCode中C++代码提示不全，智能感知功能不完整。

## 解决方案

### 1. 确保安装了必要的扩展
- **C/C++** (Microsoft) - 必须安装
- **C/C++ Extension Pack** (Microsoft) - 推荐安装
- **Code Runner** - 可选，用于快速运行代码

### 2. 配置文件已更新
已经为您配置了以下文件：

#### `.vscode/c_cpp_properties.json`
- 配置了正确的编译器路径
- 添加了完整的包含路径
- 设置了C++17标准
- 配置了MinGW64特定的宏定义

#### `.vscode/settings.json`
- 配置了文件关联
- 设置了智能感知引擎参数
- 添加了完整的包含路径
- 启用了增强功能

### 3. 重建IntelliSense数据库

#### 方法1：使用命令面板（推荐）
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `C/C++: Reset IntelliSense Database`
3. 选择并执行该命令
4. 等待重建完成（可能需要几分钟）

#### 方法2：重新扫描工作区
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `C/C++: Rescan Workspace`
3. 选择并执行该命令

#### 方法3：重新加载窗口
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `Developer: Reload Window`
3. 选择并执行该命令

### 4. 验证配置是否生效

打开 `intellisense_test.cpp` 文件，检查以下功能：

#### 应该工作的功能：
- ✅ 语法高亮（关键字、类型、字符串等有颜色）
- ✅ 自动补全（输入 `vec.` 后显示方法列表）
- ✅ 参数提示（函数调用时显示参数信息）
- ✅ 错误检测（红色波浪线标记错误）
- ✅ 符号导航（Ctrl+点击跳转到定义）
- ✅ 悬停提示（鼠标悬停显示类型信息）

#### 测试步骤：
1. 在 `vec.` 后面输入字符，应该看到方法列表
2. 在 `str.` 后面输入字符，应该看到string方法
3. 输入 `std::` 应该看到标准库命名空间内容
4. 鼠标悬停在变量上应该显示类型信息

### 5. 故障排除

#### 如果智能感知仍然不工作：

1. **检查C/C++扩展状态**
   - 确保扩展已启用
   - 尝试禁用后重新启用

2. **检查编译器路径**
   - 确认 `C:/mingw64/bin/g++.exe` 存在
   - 在终端运行 `g++ --version` 验证

3. **查看输出日志**
   - 打开输出面板（View → Output）
   - 选择 "C/C++" 频道
   - 查看是否有错误信息

4. **清除缓存**
   - 关闭VSCode
   - 删除 `.vscode/ipch` 文件夹
   - 删除 `.vscode/browse.vc.db` 文件
   - 重新打开VSCode

5. **检查工作区设置**
   - 确保在正确的工作区中
   - 检查是否有冲突的全局设置

### 6. 高级配置选项

如果需要进一步优化，可以调整以下设置：

```json
{
    "C_Cpp.intelliSenseUpdateDelay": 2000,
    "C_Cpp.workspaceParsingPriority": "highest",
    "C_Cpp.intelliSenseCacheSize": 5120,
    "C_Cpp.loggingLevel": "Information"
}
```

### 7. 常见问题

**Q: 为什么有些STL函数没有提示？**
A: 确保包含了正确的头文件，并且IntelliSense数据库已完全构建。

**Q: 编译可以通过但是有红色波浪线？**
A: 这可能是IntelliSense配置问题，尝试重建数据库。

**Q: 智能感知很慢？**
A: 可以调整 `intelliSenseUpdateDelay` 和缓存大小设置。

### 8. 验证成功标志

当配置成功后，您应该看到：
- 状态栏右下角显示 "C/C++ Ready"
- 代码有正确的语法高亮
- 输入时有自动补全提示
- 错误代码有红色波浪线标记
- 鼠标悬停有类型信息显示
