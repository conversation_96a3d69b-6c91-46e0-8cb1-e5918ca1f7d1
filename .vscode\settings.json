{"files.associations": {"*.cpp": "cpp", "*.h": "c", "*.hpp": "cpp", "iostream": "cpp", "vector": "cpp", "string": "cpp", "algorithm": "cpp", "map": "cpp", "set": "cpp", "queue": "cpp", "stack": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "memory": "cpp", "utility": "cpp", "functional": "cpp", "iterator": "cpp", "numeric": "cpp", "array": "cpp", "bitset": "cpp", "chrono": "cpp", "cmath": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "fstream": "cpp", "iomanip": "cpp", "limits": "cpp", "random": "cpp", "regex": "cpp", "sstream": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp"}, "code-runner.executorMap": {"cpp": "cd $dir && set PATH=C:\\mingw64\\bin;%PATH% && g++ -std=c++17 -g $fileName -o $fileNameWithoutExt.exe && $fileNameWithoutExt.exe"}, "code-runner.runInTerminal": true, "code-runner.saveFileBeforeRun": true, "code-runner.clearPreviousOutput": true, "C_Cpp.default.compilerPath": "C:/mingw64/bin/g++.exe", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.cStandard": "c17", "C_Cpp.default.intelliSenseMode": "windows-gcc-x64", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.intelliSenseEngineFallback": "enabled", "C_Cpp.autocomplete": "default", "C_Cpp.errorSquiggles": "enabled", "C_Cpp.dimInactiveRegions": true, "C_Cpp.enhancedColorization": "enabled", "C_Cpp.suggestSnippets": true, "C_Cpp.default.includePath": ["${workspaceFolder}/**", "C:/mingw64/include/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/**", "C:/mingw64/x86_64-w64-mingw32/include/**"], "C_Cpp.default.defines": ["_DEBUG", "UNICODE", "_UNICODE", "__GNUC__", "__MINGW32__", "__MINGW64__"], "C_Cpp.default.browse.path": ["${workspaceFolder}/**", "C:/mingw64/include/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/**", "C:/mingw64/x86_64-w64-mingw32/include/**"], "C_Cpp.default.browse.limitSymbolsToIncludedHeaders": true, "C_Cpp.default.browse.databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db", "C_Cpp.loggingLevel": "Information", "C_Cpp.workspaceParsingPriority": "highest", "C_Cpp.intelliSenseCachePath": "${workspaceFolder}/.vscode/ipch", "C_Cpp.intelliSenseCacheSize": 5120, "C_Cpp.intelliSenseUpdateDelay": 2000, "C_Cpp.experimentalFeatures": "enabled", "C_Cpp.vcpkg.enabled": false, "terminal.integrated.defaultProfile.windows": "Command Prompt"}