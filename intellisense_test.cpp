#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
#include <map>
#include <set>
#include <queue>
#include <stack>

using namespace std;

int main() {
    // 测试vector智能感知
    vector<int> vec = {1, 2, 3, 4, 5};
    vec.push_back(6);  // 这里应该有智能感知提示
    vec.size();        // 这里应该有智能感知提示
    
    // 测试string智能感知
    string str = "Hello World";
    str.length();      // 这里应该有智能感知提示
    str.substr(0, 5);  // 这里应该有智能感知提示
    
    // 测试算法智能感知
    sort(vec.begin(), vec.end());  // 这里应该有智能感知提示
    
    // 测试map智能感知
    map<string, int> m;
    m.insert(make_pair("key", 1));  // 这里应该有智能感知提示
    
    // 测试cout智能感知
    cout << "Testing IntelliSense" << endl;  // 这里应该有智能感知提示
    
    return 0;
}
