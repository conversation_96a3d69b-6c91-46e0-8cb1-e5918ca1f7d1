# 重建C++智能感知的步骤

## 方法1：通过VSCode命令面板
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入并选择 `C/C++: Reset IntelliSense Database`
3. 等待重建完成

## 方法2：重新加载窗口
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入并选择 `Developer: Reload Window`

## 方法3：手动删除缓存
1. 关闭VSCode
2. 删除 `.vscode/ipch` 文件夹（如果存在）
3. 删除 `.vscode/browse.vc.db` 文件（如果存在）
4. 重新打开VSCode

## 方法4：强制重新解析
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入并选择 `C/C++: Rescan Workspace`

## 检查智能感知是否工作
打开 `intellisense_test.cpp` 文件，在以下位置应该看到智能感知提示：
- `vec.` 后面应该显示vector的方法列表
- `str.` 后面应该显示string的方法列表
- `cout` 应该有语法高亮
- 函数参数应该有提示

## 如果仍然不工作
1. 检查C/C++扩展是否已安装并启用
2. 检查编译器路径是否正确：`C:/mingw64/bin/g++.exe`
3. 查看输出面板中的C/C++日志信息
