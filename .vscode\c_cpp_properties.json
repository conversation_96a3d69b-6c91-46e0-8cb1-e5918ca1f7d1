{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "C:/mingw64/include/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/**", "C:/mingw64/x86_64-w64-mingw32/include/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "__GNUC__", "__MINGW32__", "__MINGW64__"], "windowsSdkVersion": "10.0.19041.0", "compilerPath": "C:/mingw64/bin/g++.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64", "compilerArgs": ["-Wall", "-Wextra", "-Wpedantic"], "browse": {"path": ["${workspaceFolder}/**", "C:/mingw64/include/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/**", "C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/**", "C:/mingw64/x86_64-w64-mingw32/include/**"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db"}}], "version": 4}